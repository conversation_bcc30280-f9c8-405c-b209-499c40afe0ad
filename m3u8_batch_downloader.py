#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
M3U8批量下载器
用于批量下载洋葱培优课程的m3u8视频文件

功能特性：
- 扫描所有m3u8文件并建立索引
- 支持选择性下载（全部、按目录、单文件）
- 保持原有目录结构
- 实时进度显示
- 断点续传支持
- 详细日志记录

作者：AI Assistant
版本：1.0.0
"""

import os
import sys
import json
import subprocess
import threading
import queue
import time
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime

try:
    from colorama import init, Fore, Back, Style
    init(autoreset=True)
    COLORAMA_AVAILABLE = True
except ImportError:
    COLORAMA_AVAILABLE = False
    # 定义空的颜色常量
    class Fore:
        RED = GREEN = YELLOW = BLUE = MAGENTA = CYAN = WHITE = RESET = ""
    class Back:
        RED = GREEN = YELLOW = BLUE = MAGENTA = CYAN = WHITE = RESET = ""
    class Style:
        DIM = NORMAL = BRIGHT = RESET_ALL = ""

try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False


@dataclass
class M3U8File:
    """M3U8文件信息数据类"""
    path: str
    relative_path: str
    size: int = 0
    downloaded: bool = False
    download_path: str = ""


@dataclass
class DownloadTask:
    """下载任务数据类"""
    m3u8_file: M3U8File
    priority: int = 0
    retry_count: int = 0
    max_retries: int = 3


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.default_config = {
            "download_dir": "downloads",
            "log_dir": "logs",
            "max_concurrent": 3,
            "max_retries": 3,
            "n_m3u8dl_path": "./N_m3u8DL-CLI_v3.0.2.exe",
            "ffmpeg_path": "./ffmpeg.exe",
            "download_quality": "best",
            "keep_temp_files": False,
            "auto_merge": True
        }
        self.config = self.load_config()
    
    def load_config(self) -> dict:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                for key, value in self.default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            except Exception as e:
                print(f"{Fore.YELLOW}配置文件加载失败，使用默认配置: {e}")
                return self.default_config.copy()
        else:
            return self.default_config.copy()
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"{Fore.RED}配置文件保存失败: {e}")
            return False
    
    def get(self, key: str, default=None):
        """获取配置项"""
        return self.config.get(key, default)
    
    def set(self, key: str, value):
        """设置配置项"""
        self.config[key] = value


class Logger:
    """日志管理器"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 设置日志格式
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        
        # 创建日志文件名（包含时间戳）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = self.log_dir / f"download_{timestamp}.log"
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def info(self, message: str):
        """记录信息日志"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """记录错误日志"""
        self.logger.error(message)
    
    def debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(message)


class M3U8Scanner:
    """M3U8文件扫描器"""

    def __init__(self, root_path: str = "."):
        self.root_path = Path(root_path)
        self.m3u8_files: List[M3U8File] = []
        self.file_tree: Dict = {}
        self.total_files = 0

    def scan_files(self) -> List[M3U8File]:
        """扫描所有m3u8文件"""
        print(f"{Fore.CYAN}正在扫描m3u8文件...")

        self.m3u8_files.clear()
        self.file_tree.clear()

        # 递归扫描所有.m3u8文件
        for m3u8_path in self.root_path.rglob("*.m3u8"):
            if m3u8_path.is_file():
                relative_path = m3u8_path.relative_to(self.root_path)

                # 创建M3U8File对象
                m3u8_file = M3U8File(
                    path=str(m3u8_path),
                    relative_path=str(relative_path),
                    size=m3u8_path.stat().st_size if m3u8_path.exists() else 0
                )

                self.m3u8_files.append(m3u8_file)

                # 构建文件树结构
                self._add_to_tree(relative_path)

        self.total_files = len(self.m3u8_files)
        print(f"{Fore.GREEN}扫描完成！找到 {self.total_files} 个m3u8文件")

        return self.m3u8_files

    def _add_to_tree(self, relative_path: Path):
        """将文件添加到树结构中"""
        parts = relative_path.parts
        current = self.file_tree

        # 构建目录树
        for part in parts[:-1]:  # 除了文件名的所有部分
            if part not in current:
                current[part] = {"_files": [], "_dirs": {}}
            current = current[part]["_dirs"]

        # 添加文件到最后一级目录
        if parts:
            parent_dir = parts[-2] if len(parts) > 1 else "."
            if parent_dir not in current:
                current[parent_dir] = {"_files": [], "_dirs": {}}
            current[parent_dir]["_files"].append(relative_path.name)

    def get_directory_structure(self) -> Dict:
        """获取目录结构"""
        return self.file_tree

    def get_files_by_directory(self, directory: str) -> List[M3U8File]:
        """根据目录获取文件列表"""
        if directory == "all":
            return self.m3u8_files

        filtered_files = []
        for m3u8_file in self.m3u8_files:
            if directory in m3u8_file.relative_path:
                filtered_files.append(m3u8_file)

        return filtered_files

    def print_file_tree(self, max_depth: int = 2):
        """打印文件树结构"""
        print(f"\n{Fore.YELLOW}=== 课程目录结构 ===")
        self._print_tree_recursive(self.file_tree, "", 0, max_depth)

    def _print_tree_recursive(self, tree: Dict, prefix: str, depth: int, max_depth: int):
        """递归打印树结构"""
        if depth >= max_depth:
            return

        for i, (name, content) in enumerate(tree.items()):
            if name.startswith("_"):
                continue

            is_last = i == len([k for k in tree.keys() if not k.startswith("_")]) - 1
            current_prefix = "└── " if is_last else "├── "

            file_count = len(content.get("_files", []))
            dir_count = len([k for k in content.get("_dirs", {}).keys() if not k.startswith("_")])

            info = f"({file_count} 文件" + (f", {dir_count} 子目录" if dir_count > 0 else "") + ")"
            print(f"{prefix}{current_prefix}{Fore.CYAN}{name} {Fore.WHITE}{info}")

            # 递归打印子目录
            if depth < max_depth - 1 and "_dirs" in content:
                next_prefix = prefix + ("    " if is_last else "│   ")
                self._print_tree_recursive(content["_dirs"], next_prefix, depth + 1, max_depth)


class UIManager:
    """用户界面管理器"""

    def __init__(self, scanner: M3U8Scanner):
        self.scanner = scanner

    def show_main_menu(self) -> str:
        """显示主菜单"""
        print(f"\n{Fore.YELLOW}=== 主菜单 ===")
        print(f"{Fore.WHITE}1. 下载所有课程")
        print(f"{Fore.WHITE}2. 选择课程类别下载")
        print(f"{Fore.WHITE}3. 选择具体目录下载")
        print(f"{Fore.WHITE}4. 查看目录结构")
        print(f"{Fore.WHITE}5. 设置下载参数")
        print(f"{Fore.WHITE}6. 退出程序")

        while True:
            try:
                choice = input(f"\n{Fore.GREEN}请选择操作 (1-6): ").strip()
                if choice in ['1', '2', '3', '4', '5', '6']:
                    return choice
                else:
                    print(f"{Fore.RED}无效选择，请输入1-6之间的数字")
            except KeyboardInterrupt:
                print(f"\n{Fore.YELLOW}程序已取消")
                return '6'

    def show_category_menu(self) -> List[str]:
        """显示课程类别菜单"""
        # 获取顶级目录
        categories = []
        for name in self.scanner.file_tree.keys():
            if not name.startswith("_"):
                categories.append(name)

        if not categories:
            print(f"{Fore.RED}未找到课程类别")
            return []

        print(f"\n{Fore.YELLOW}=== 课程类别 ===")
        for i, category in enumerate(categories, 1):
            file_count = self._count_files_in_category(category)
            print(f"{Fore.WHITE}{i}. {category} ({file_count} 个文件)")

        print(f"{Fore.WHITE}{len(categories) + 1}. 全选")
        print(f"{Fore.WHITE}{len(categories) + 2}. 返回主菜单")

        while True:
            try:
                choice = input(f"\n{Fore.GREEN}请选择课程类别 (多选用逗号分隔): ").strip()
                if not choice:
                    continue

                if choice == str(len(categories) + 2):  # 返回主菜单
                    return []

                if choice == str(len(categories) + 1):  # 全选
                    return categories

                # 解析多选
                selected_indices = []
                for part in choice.split(','):
                    part = part.strip()
                    if part.isdigit():
                        idx = int(part) - 1
                        if 0 <= idx < len(categories):
                            selected_indices.append(idx)

                if selected_indices:
                    return [categories[i] for i in selected_indices]
                else:
                    print(f"{Fore.RED}无效选择，请重新输入")

            except KeyboardInterrupt:
                return []

    def _count_files_in_category(self, category: str) -> int:
        """统计类别中的文件数量"""
        count = 0
        for m3u8_file in self.scanner.m3u8_files:
            if m3u8_file.relative_path.startswith(category):
                count += 1
        return count

    def confirm_download(self, selected_files: List[M3U8File], download_dir: str) -> bool:
        """确认下载"""
        print(f"\n{Fore.YELLOW}=== 下载确认 ===")
        print(f"{Fore.WHITE}选择的文件数量: {len(selected_files)}")
        print(f"{Fore.WHITE}下载目录: {download_dir}")

        # 显示前几个文件作为示例
        print(f"\n{Fore.CYAN}文件示例:")
        for i, file in enumerate(selected_files[:5]):
            print(f"  {i+1}. {file.relative_path}")

        if len(selected_files) > 5:
            print(f"  ... 还有 {len(selected_files) - 5} 个文件")

        while True:
            try:
                confirm = input(f"\n{Fore.GREEN}确认开始下载? (y/n): ").strip().lower()
                if confirm in ['y', 'yes', '是']:
                    return True
                elif confirm in ['n', 'no', '否']:
                    return False
                else:
                    print(f"{Fore.RED}请输入 y 或 n")
            except KeyboardInterrupt:
                return False

    def show_progress(self, current: int, total: int, current_file: str = ""):
        """显示下载进度"""
        if TQDM_AVAILABLE:
            return  # tqdm会处理进度显示

        percentage = (current / total) * 100 if total > 0 else 0
        bar_length = 50
        filled_length = int(bar_length * current // total) if total > 0 else 0

        bar = '█' * filled_length + '-' * (bar_length - filled_length)

        print(f"\r{Fore.CYAN}进度: |{bar}| {current}/{total} ({percentage:.1f}%) {current_file}", end='')

        if current == total:
            print()  # 换行


class DownloadManager:
    """下载管理器"""

    def __init__(self, config: ConfigManager, logger: Logger):
        self.config = config
        self.logger = logger
        self.download_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.active_downloads = 0
        self.completed_downloads = 0
        self.failed_downloads = 0
        self.total_downloads = 0
        self.stop_event = threading.Event()

        # 验证工具是否存在
        self.n_m3u8dl_path = Path(self.config.get("n_m3u8dl_path"))
        self.ffmpeg_path = Path(self.config.get("ffmpeg_path"))

        if not self.n_m3u8dl_path.exists():
            raise FileNotFoundError(f"N_m3u8DL-CLI工具未找到: {self.n_m3u8dl_path}")
        if not self.ffmpeg_path.exists():
            raise FileNotFoundError(f"FFmpeg工具未找到: {self.ffmpeg_path}")

    def download_files(self, m3u8_files: List[M3U8File], download_dir: str) -> bool:
        """批量下载文件"""
        self.total_downloads = len(m3u8_files)
        self.completed_downloads = 0
        self.failed_downloads = 0

        # 创建下载目录
        download_path = Path(download_dir)
        download_path.mkdir(parents=True, exist_ok=True)

        # 准备下载任务
        for m3u8_file in m3u8_files:
            # 设置输出路径
            output_dir = download_path / Path(m3u8_file.relative_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # 设置输出文件名（去掉.m3u8扩展名）
            output_name = Path(m3u8_file.relative_path).stem
            m3u8_file.download_path = str(output_dir / output_name)

            # 检查是否已下载
            if self._is_already_downloaded(m3u8_file):
                self.logger.info(f"跳过已下载文件: {m3u8_file.relative_path}")
                self.completed_downloads += 1
                continue

            # 创建下载任务
            task = DownloadTask(
                m3u8_file=m3u8_file,
                max_retries=self.config.get("max_retries", 3)
            )
            self.download_queue.put(task)

        # 启动下载线程
        max_concurrent = self.config.get("max_concurrent", 3)
        threads = []

        for _ in range(min(max_concurrent, self.download_queue.qsize())):
            thread = threading.Thread(target=self._download_worker, daemon=True)
            thread.start()
            threads.append(thread)

        # 监控下载进度
        self._monitor_progress()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 输出结果
        success_rate = (self.completed_downloads / self.total_downloads) * 100 if self.total_downloads > 0 else 0

        print(f"\n{Fore.GREEN}下载完成!")
        print(f"{Fore.WHITE}总计: {self.total_downloads} 个文件")
        print(f"{Fore.GREEN}成功: {self.completed_downloads} 个")
        print(f"{Fore.RED}失败: {self.failed_downloads} 个")
        print(f"{Fore.CYAN}成功率: {success_rate:.1f}%")

        return self.failed_downloads == 0

    def _download_worker(self):
        """下载工作线程"""
        while not self.stop_event.is_set():
            try:
                task = self.download_queue.get(timeout=1)
                self.active_downloads += 1

                success = self._download_single_file(task)

                if success:
                    self.completed_downloads += 1
                    self.logger.info(f"下载成功: {task.m3u8_file.relative_path}")
                else:
                    # 重试机制
                    if task.retry_count < task.max_retries:
                        task.retry_count += 1
                        self.download_queue.put(task)
                        self.logger.warning(f"下载失败，重试 {task.retry_count}/{task.max_retries}: {task.m3u8_file.relative_path}")
                    else:
                        self.failed_downloads += 1
                        self.logger.error(f"下载失败（已达最大重试次数）: {task.m3u8_file.relative_path}")

                self.active_downloads -= 1
                self.download_queue.task_done()

            except queue.Empty:
                if self.download_queue.empty() and self.active_downloads == 0:
                    break
            except Exception as e:
                self.logger.error(f"下载线程异常: {e}")
                self.active_downloads -= 1

    def _download_single_file(self, task: DownloadTask) -> bool:
        """下载单个文件"""
        try:
            m3u8_file = task.m3u8_file

            # 构建N_m3u8DL-CLI命令
            cmd = [
                str(self.n_m3u8dl_path),
                m3u8_file.path,
                "--workDir", str(Path(m3u8_file.download_path).parent),
                "--saveName", Path(m3u8_file.download_path).name,
                "--enableDelAfterDone"
            ]

            # 添加FFmpeg路径
            if self.ffmpeg_path.exists():
                cmd.extend(["--ffmpegPath", str(self.ffmpeg_path)])

            # 执行下载命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                timeout=3600  # 1小时超时
            )

            if result.returncode == 0:
                return True
            else:
                self.logger.error(f"下载命令失败: {' '.join(cmd)}")
                self.logger.error(f"错误输出: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            self.logger.error(f"下载超时: {task.m3u8_file.relative_path}")
            return False
        except Exception as e:
            self.logger.error(f"下载异常: {task.m3u8_file.relative_path}, 错误: {e}")
            return False

    def _is_already_downloaded(self, m3u8_file: M3U8File) -> bool:
        """检查文件是否已下载"""
        # 检查可能的输出文件格式
        possible_extensions = ['.mp4', '.mkv', '.ts', '.flv']
        base_path = Path(m3u8_file.download_path)

        for ext in possible_extensions:
            if (base_path.parent / (base_path.name + ext)).exists():
                return True

        return False

    def _monitor_progress(self):
        """监控下载进度"""
        if TQDM_AVAILABLE:
            with tqdm(total=self.total_downloads, desc="下载进度", unit="文件") as pbar:
                last_completed = 0
                while self.completed_downloads + self.failed_downloads < self.total_downloads:
                    current_completed = self.completed_downloads
                    if current_completed > last_completed:
                        pbar.update(current_completed - last_completed)
                        last_completed = current_completed
                    time.sleep(0.5)
                pbar.update(self.total_downloads - last_completed)
        else:
            while self.completed_downloads + self.failed_downloads < self.total_downloads:
                print(f"\r{Fore.CYAN}下载进度: {self.completed_downloads + self.failed_downloads}/{self.total_downloads} "
                      f"(成功: {self.completed_downloads}, 失败: {self.failed_downloads})", end='')
                time.sleep(1)
            print()


class M3U8BatchDownloader:
    """M3U8批量下载器主应用程序"""

    def __init__(self):
        self.config = ConfigManager()
        self.logger = Logger(self.config.get("log_dir"))
        self.scanner = M3U8Scanner()
        self.ui = UIManager(self.scanner)
        self.downloader = None

        # 初始化下载器
        try:
            self.downloader = DownloadManager(self.config, self.logger)
        except FileNotFoundError as e:
            print(f"{Fore.RED}初始化失败: {e}")
            print(f"{Fore.YELLOW}请确保 N_m3u8DL-CLI_v3.0.2.exe 和 ffmpeg.exe 在当前目录下")
            sys.exit(1)

    def run(self):
        """运行主程序"""
        print(f"{Fore.GREEN}M3U8批量下载器 v1.0.0")
        print(f"{Fore.CYAN}正在初始化...")

        # 扫描m3u8文件
        try:
            self.scanner.scan_files()
        except Exception as e:
            print(f"{Fore.RED}扫描文件失败: {e}")
            return

        if not self.scanner.m3u8_files:
            print(f"{Fore.RED}未找到任何m3u8文件")
            return

        # 主循环
        while True:
            try:
                choice = self.ui.show_main_menu()

                if choice == '1':
                    self._download_all()
                elif choice == '2':
                    self._download_by_category()
                elif choice == '3':
                    self._download_by_directory()
                elif choice == '4':
                    self._show_directory_structure()
                elif choice == '5':
                    self._show_settings()
                elif choice == '6':
                    print(f"{Fore.GREEN}感谢使用！")
                    break

            except KeyboardInterrupt:
                print(f"\n{Fore.YELLOW}程序已取消")
                break
            except Exception as e:
                print(f"{Fore.RED}程序异常: {e}")
                self.logger.error(f"程序异常: {e}")

    def _download_all(self):
        """下载所有文件"""
        download_dir = self.config.get("download_dir")

        if self.ui.confirm_download(self.scanner.m3u8_files, download_dir):
            print(f"{Fore.CYAN}开始下载所有文件...")
            self.downloader.download_files(self.scanner.m3u8_files, download_dir)

    def _download_by_category(self):
        """按类别下载"""
        selected_categories = self.ui.show_category_menu()

        if not selected_categories:
            return

        # 获取选中类别的所有文件
        selected_files = []
        for category in selected_categories:
            files = self.scanner.get_files_by_directory(category)
            selected_files.extend(files)

        if not selected_files:
            print(f"{Fore.RED}选中的类别中没有文件")
            return

        download_dir = self.config.get("download_dir")

        if self.ui.confirm_download(selected_files, download_dir):
            print(f"{Fore.CYAN}开始下载选中的类别...")
            self.downloader.download_files(selected_files, download_dir)

    def _download_by_directory(self):
        """按目录下载"""
        print(f"{Fore.YELLOW}请输入要下载的目录路径（相对路径）:")
        print(f"{Fore.WHITE}例如: 初中数学新中考培优课-全国版")
        print(f"{Fore.WHITE}或者: 小学数学思维培优课/一年级上册")

        try:
            directory = input(f"{Fore.GREEN}目录路径: ").strip()
            if not directory:
                return

            selected_files = self.scanner.get_files_by_directory(directory)

            if not selected_files:
                print(f"{Fore.RED}指定目录中没有找到文件")
                return

            download_dir = self.config.get("download_dir")

            if self.ui.confirm_download(selected_files, download_dir):
                print(f"{Fore.CYAN}开始下载指定目录...")
                self.downloader.download_files(selected_files, download_dir)

        except KeyboardInterrupt:
            return

    def _show_directory_structure(self):
        """显示目录结构"""
        self.scanner.print_file_tree()

        print(f"\n{Fore.CYAN}统计信息:")
        print(f"{Fore.WHITE}总文件数: {self.scanner.total_files}")

        # 按顶级目录统计
        category_stats = {}
        for m3u8_file in self.scanner.m3u8_files:
            category = m3u8_file.relative_path.split(os.sep)[0]
            category_stats[category] = category_stats.get(category, 0) + 1

        print(f"\n{Fore.YELLOW}各类别文件数:")
        for category, count in sorted(category_stats.items()):
            print(f"{Fore.WHITE}  {category}: {count} 个文件")

    def _show_settings(self):
        """显示和修改设置"""
        print(f"\n{Fore.YELLOW}=== 当前设置 ===")
        print(f"{Fore.WHITE}1. 下载目录: {self.config.get('download_dir')}")
        print(f"{Fore.WHITE}2. 最大并发数: {self.config.get('max_concurrent')}")
        print(f"{Fore.WHITE}3. 最大重试次数: {self.config.get('max_retries')}")
        print(f"{Fore.WHITE}4. 下载质量: {self.config.get('download_quality')}")
        print(f"{Fore.WHITE}5. 自动合并: {self.config.get('auto_merge')}")
        print(f"{Fore.WHITE}6. 保存配置并返回")

        while True:
            try:
                choice = input(f"\n{Fore.GREEN}选择要修改的设置 (1-6): ").strip()

                if choice == '1':
                    new_dir = input(f"新的下载目录: ").strip()
                    if new_dir:
                        self.config.set('download_dir', new_dir)
                elif choice == '2':
                    new_concurrent = input(f"新的最大并发数 (1-10): ").strip()
                    if new_concurrent.isdigit() and 1 <= int(new_concurrent) <= 10:
                        self.config.set('max_concurrent', int(new_concurrent))
                elif choice == '3':
                    new_retries = input(f"新的最大重试次数 (1-10): ").strip()
                    if new_retries.isdigit() and 1 <= int(new_retries) <= 10:
                        self.config.set('max_retries', int(new_retries))
                elif choice == '4':
                    print(f"可选质量: best, worst, auto")
                    new_quality = input(f"新的下载质量: ").strip()
                    if new_quality in ['best', 'worst', 'auto']:
                        self.config.set('download_quality', new_quality)
                elif choice == '5':
                    new_merge = input(f"自动合并 (y/n): ").strip().lower()
                    if new_merge in ['y', 'yes', 'n', 'no']:
                        self.config.set('auto_merge', new_merge in ['y', 'yes'])
                elif choice == '6':
                    self.config.save_config()
                    print(f"{Fore.GREEN}配置已保存")
                    break
                else:
                    print(f"{Fore.RED}无效选择")

            except KeyboardInterrupt:
                break


def main():
    """主函数"""
    try:
        app = M3U8BatchDownloader()
        app.run()
    except Exception as e:
        print(f"{Fore.RED}程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
